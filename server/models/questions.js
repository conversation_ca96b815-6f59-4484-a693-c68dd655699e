const mongoose = require('mongoose');

const questionSchema = new mongoose.Schema({
  qid:        { type: Number, required: true, unique: true },
  title:       { type: String, required: true },
  description: { type: String, required: true },
  difficulty:  {
    type: String,
    required: true,
    enum: ['Easy', 'Medium', 'Hard'],
    default: 'Easy'
  },
  testInput:   { type: String, required: true },
  testOutput:  { type: String, required: true },
  createdAt:   { type: Date, default: Date.now }
});

module.exports = mongoose.model('Question', questionSchema);