{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.11.0", "cookie-session": "^2.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "postcss": "^8.5.6"}}