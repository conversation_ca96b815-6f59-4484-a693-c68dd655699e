:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

html, body, #root {
  width: 100%;
  margin: 0;
  padding: 0;
}

html, body {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Two-column layout for forms */
.two-column-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  gap: 24px;
  padding: 24px;
  background-color: #242424;
  position: relative;
}

.two-column-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(100, 108, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(83, 91, 242, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.two-column-wrapper > div {
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: rgba(26, 26, 26, 0.95);
  border-radius: 20px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 1;
}

.two-column-wrapper .mb-6 {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.two-column-wrapper label {
  display: block;
  width: 100%;
}

.two-column-wrapper textarea,
.two-column-wrapper input {
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1);
  background: rgba(55, 55, 55, 0.8);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.two-column-wrapper input {
  height: 56px;
  font-size: 18px;
  font-weight: 500;
  padding: 16px 20px;
}

.two-column-wrapper textarea::placeholder,
.two-column-wrapper input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.two-column-wrapper textarea:focus,
.two-column-wrapper input:focus {
  border-color: #646cff;
  box-shadow: 
    0 0 0 4px rgba(100, 108, 255, 0.2),
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  background: rgba(75, 75, 75, 0.9);
  text-align: left;
}

.two-column-wrapper textarea:not(:placeholder-shown),
.two-column-wrapper input:not(:placeholder-shown) {
  text-align: left;
}

.two-column-wrapper textarea {
  min-height: 200px;
  flex: 1;
}

.two-column-wrapper .flex-1 {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.two-column-wrapper .flex-1 textarea {
  flex: 1;
  min-height: 300px;
}

/* Submit button container */
.submit-container {
  position: fixed;
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.submit-btn {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 100%);
  border: none;
  border-radius: 60px;
  padding: 18px 48px;
  color: white;
  font-weight: 700;
  font-size: 17px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 10px 25px rgba(100, 108, 255, 0.4),
    0 4px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:hover {
  background: linear-gradient(135deg, #535bf2 0%, #4338ca 100%);
  transform: translateY(-4px);
  box-shadow: 
    0 20px 40px rgba(100, 108, 255, 0.6),
    0 8px 16px rgba(0, 0, 0, 0.4);
}

.submit-btn:active {
  transform: translateY(-2px);
}

/* Dialog box styles */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dialog-box {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  max-width: 420px;
  text-align: center;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 10px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  color: rgba(255, 255, 255, 0.9);
}

.dialog-success {
  border-left: 6px solid #10b981;
}

.dialog-error {
  border-left: 6px solid #ef4444;
}

.dialog-icon {
  width: 72px;
  height: 72px;
  margin: 0 auto 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dialog-success .dialog-icon {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.25) 100%);
  color: #10b981;
  border: 2px solid rgba(16, 185, 129, 0.3);
}

.dialog-error .dialog-icon {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.25) 100%);
  color: #ef4444;
  border: 2px solid rgba(239, 68, 68, 0.3);
}

@keyframes fadeIn {
  from { 
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to { 
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
    filter: blur(4px);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

/* Additional professional styling */
.two-column-wrapper h1 {
  background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.75rem;
  font-weight: 800;
  letter-spacing: -0.025em;
  margin-bottom: 1.5rem;
}

.two-column-wrapper label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  margin-bottom: 0.75rem;
}

@media (max-width: 768px) {
  .two-column-wrapper {
    grid-template-columns: 1fr;
    height: auto;
    max-height: none;
    overflow: visible;
    padding: 15px;
    gap: 15px;
  }
  
  .two-column-wrapper textarea {
    min-height: 150px;
  }
  
  .submit-container {
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    margin: 20px 0;
    text-align: center;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
