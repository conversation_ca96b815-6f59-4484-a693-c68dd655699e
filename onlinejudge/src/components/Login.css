/* Login Component Styles */
html, body {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

#root {
  padding: 0 !important;
  margin: 0 !important;
}

.onboarding-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background Elements */
.background-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(100, 108, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(100, 108, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  z-index: 1;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  color: rgba(100, 108, 255, 0.3);
  font-size: 1.5rem;
  font-weight: 600;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* Main Content */
.onboarding-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem 2rem 4rem;
  gap: 4rem;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-top: 2rem;
  width: 100%;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.logo-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, #646cff, #535bf2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 20px rgba(100, 108, 255, 0.3));
}

.platform-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(135deg, #646cff, #535bf2, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
  text-shadow: 0 0 30px rgba(100, 108, 255, 0.2);
}

.platform-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Features Panel */
.features-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 600px;
}



.features-showcase {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  width: 100%;
  max-width: 700px;
  position: relative;
  overflow: hidden;
}

.feature-carousel {
  position: relative;
  min-height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-card {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
  padding: 2rem;
}

.feature-card.active {
  opacity: 1;
  transform: translateY(0);
}

.feature-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 0 20px rgba(100, 108, 255, 0.3));
}

.feature-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 1rem;
  color: #ffffff;
}

.feature-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 1rem;
  line-height: 1.6;
}

.feature-details {
  font-size: 0.9rem;
  color: rgba(100, 108, 255, 0.8);
  font-style: italic;
}

.feature-indicators {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 2rem;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #646cff;
  transform: scale(1.2);
}

/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  width: 100%;
  max-width: 600px;
  padding: 2rem;
  background: rgba(26, 26, 26, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.stat-item {
  text-align: center;
  padding: 1rem;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #646cff, #535bf2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* CTA Section */
.cta-section {
  text-align: center;
  width: 100%;
  max-width: 500px;
  padding: 3rem 2rem;
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem;
  background: linear-gradient(135deg, #646cff, #535bf2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 2.5rem;
  line-height: 1.6;
}

.google-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  max-width: 300px;
  margin: 0 auto 2rem;
  padding: 1rem 2rem;
  background: #ffffff;
  color: #1f1f1f;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.google-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.google-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: block;
}

.login-benefits {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.benefit-icon {
  color: #4ade80;
  font-weight: bold;
}

.feature-content {
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  display: block;
  filter: drop-shadow(0 0 20px rgba(100, 108, 255, 0.3));
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #ffffff;
}

.feature-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 1rem;
  line-height: 1.6;
}

.feature-details {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-style: italic;
}

.feature-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #646cff;
  transform: scale(1.2);
}





.divider {
  display: flex;
  align-items: center;
  margin: 2rem 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.9rem;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.divider::before {
  margin-right: 1rem;
}

.divider::after {
  margin-left: 1rem;
}

.features-preview {
  margin-top: 2rem;
}

.preview-title {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.preview-items {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.preview-icon {
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #646cff, #535bf2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: white;
  flex-shrink: 0;
}

/* Mobile Responsiveness */
@media (max-width: 1024px) {
  .onboarding-content {
    gap: 3rem;
    padding: 1rem;
    max-width: 600px;
  }

  .platform-title {
    font-size: 2.5rem;
  }

  .platform-subtitle {
    font-size: 1.1rem;
  }

  .features-showcase {
    padding: 2rem;
  }

  .feature-title {
    font-size: 1.5rem;
  }

  .cta-title {
    font-size: 2rem;
  }

  .stats-section {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .onboarding-content {
    padding: 1rem 1rem 3rem;
    gap: 2.5rem;
  }

  .logo-section {
    flex-direction: column;
    gap: 0.5rem;
  }

  .platform-title {
    font-size: 2rem;
  }

  .platform-subtitle {
    font-size: 1rem;
    padding: 0 1rem;
  }

  .features-showcase {
    padding: 1.5rem;
  }

  .feature-icon {
    font-size: 3rem;
  }

  .feature-title {
    font-size: 1.3rem;
  }

  .feature-description {
    font-size: 1rem;
  }

  .stats-section {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .cta-section {
    padding: 2rem 1.5rem;
  }

  .cta-title {
    font-size: 1.8rem;
  }

  .google-login-btn {
    padding: 0.875rem 1.5rem;
  }
}
