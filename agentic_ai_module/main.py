import os
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Optional
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON>Template, MessagesPlaceholder
from langchain_core.messages import HumanMessage, AIMessage
from tools import get_problem_hint
from dotenv import load_dotenv


load_dotenv()
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

llm = ChatGoogleGenerativeAI(model="gemini-pro", google_api_key=GOOGLE_API_KEY, temperature=0.3)

# Create prompt template for the agent
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful AI assistant that gives feedback on programming code submissions. If you're not sure how to improve the code, you're allowed to use the available tools."),
    MessagesPlaceholder(variable_name="chat_history"),
    ("human", "{input}"),
    MessagesPlaceholder(variable_name="agent_scratchpad"),
])

# LangChain agent setup with updated API
tools = [get_problem_hint]
agent = create_tool_calling_agent(llm, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Store chat history manually since we're not using the deprecated memory
chat_history = []

app = FastAPI()

class Submission(BaseModel):
    problem_id: str
    user_code: str
    user_plan: Optional[str] = None
    language: str = "python"

class FeedbackResponse(BaseModel):
    message: str
    used_hint: bool


@app.post("/agentic-feedback", response_model=FeedbackResponse)
async def agentic_feedback(sub: Submission):
    try:
        user_input = f"""
Problem ID: {sub.problem_id}
Language: {sub.language}

User's Plan:
{sub.user_plan or "No plan provided"}

User's Code:
{sub.user_code}

If you're not sure how to improve the code, you're allowed to call the `get_problem_hint` tool using the problem ID.
Never return code — only describe logical mistakes, inefficiencies, or strategy improvements.
"""

        # Use the new invoke method with proper input format
        result = agent_executor.invoke({
            "input": user_input,
            "chat_history": chat_history
        })

        # Extract the output message
        output_message = result["output"]

        # Add to chat history for future conversations
        chat_history.extend([
            HumanMessage(content=user_input),
            AIMessage(content=output_message)
        ])

        used_hint = "hint" in output_message.lower()  # heuristic, adjust if needed
        return FeedbackResponse(message=output_message, used_hint=used_hint)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
