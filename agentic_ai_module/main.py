import os
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Optional
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain.agents import initialize_agent, AgentType
from langchain.memory import ConversationBufferMemory
from tools import get_problem_hint
from dotenv import load_dotenv


load_dotenv()
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

llm = ChatGoogleGenerativeAI(model="gemini-pro", google_api_key=GOOGLE_API_KEY, temperature=0.3)

tools = [get_problem_hint]
memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)

agent_executor = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.OPENAI_FUNCTIONS,  
    memory=memory,
    verbose=True
)

app = FastAPI()

class Submission(BaseModel):
    problem_id: str
    user_code: str
    user_plan: Optional[str] = None
    language: str = "python"

class FeedbackResponse(BaseModel):
    message: str
    used_hint: bool


@app.post("/agentic-feedback", response_model=FeedbackResponse)
async def agentic_feedback(sub: Submission):
    try:
        prompt = f"""
You are a helpful AI assistant that gives feedback on programming code submissions.

Problem ID: {sub.problem_id}
Language: {sub.language}

User's Plan:
{sub.user_plan or "No plan provided"}

User's Code:
{sub.user_code}

If you're not sure how to improve the code, you're allowed to call the `get_problem_hint` tool using the problem ID.
Never return code — only describe logical mistakes, inefficiencies, or strategy improvements.
"""

        result = agent_executor.run(prompt)

        used_hint = "hint" in result.lower()  # heuristic, adjust if needed
        return FeedbackResponse(message=result, used_hint=used_hint)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
