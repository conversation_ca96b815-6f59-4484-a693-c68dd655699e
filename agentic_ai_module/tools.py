from langchain.tools import tool
from pymongo import MongoClient
import os

# Setup Mongo
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
mongo_client = MongoClient(MONGO_URI)
hint_collection = mongo_client["online_judge"]["hints"]

@tool("get_problem_hint", return_direct=True)
def get_problem_hint(problem_id: str) -> str:
    """
    Fetch a programming problem hint from MongoDB using the problem ID.
    Returns a helpful hint string or a message if not found.
    """
    hint_doc = hint_collection.find_one({"_id": problem_id})
    return hint_doc["hint"] if hint_doc else "No hint found for this problem."