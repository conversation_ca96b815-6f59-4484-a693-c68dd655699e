from langchain.tools import tool
import os

# Mock hints for testing (replace with MongoDB when available)
MOCK_HINTS = {
    "test_problem": "Try to think about the time complexity of your solution. Can you optimize it?",
    "array_sort": "Consider using built-in sorting functions, but think about edge cases like empty arrays.",
    "two_sum": "A hash map can help you achieve O(n) time complexity instead of O(n²).",
    "fi<PERSON><PERSON><PERSON>": "Consider using dynamic programming to avoid redundant calculations.",
}

@tool("get_problem_hint", return_direct=True)
def get_problem_hint(problem_id: str) -> str:
    """
    Fetch a programming problem hint. Currently using mock data for testing.
    In production, this would connect to MongoDB.
    Returns a helpful hint string or a message if not found.
    """
    # Try to connect to MongoDB first, fall back to mock data
    try:
        from pymongo import MongoClient
        MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017")
        mongo_client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=1000)  # Quick timeout
        # Test connection
        mongo_client.admin.command('ping')
        hint_collection = mongo_client["online_judge"]["hints"]
        hint_doc = hint_collection.find_one({"_id": problem_id})
        return hint_doc["hint"] if hint_doc else f"No hint found for problem '{problem_id}' in database."
    except Exception:
        # Fall back to mock data if MongoDB is not available
        return MOCK_HINTS.get(problem_id, f"No hint available for problem '{problem_id}'. Try breaking down the problem into smaller steps.")